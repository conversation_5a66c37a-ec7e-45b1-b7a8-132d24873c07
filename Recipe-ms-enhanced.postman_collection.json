{"info": {"name": "Recipe Management System API - Complete with Smart Merge", "description": "Complete API collection for the Recipe Management System with Smart Merge functionality, comprehensive test cases, and all recipe endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "3.0.0"}, "item": [{"name": "Private Recipe Module", "description": "Private Recipe Management APIs with Smart Merge functionality", "item": [{"name": "Create Recipe", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('<PERSON><PERSON><PERSON> created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.id).to.exist;", "});", "", "// Store recipe ID for other tests", "if (pm.response.json().data && pm.response.json().data.id) {", "    pm.collectionVariables.set('recipeId', pm.response.json().data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Grilled Salmon with Herbs", "type": "text", "description": "Recipe title (required, 3-100 characters)"}, {"key": "recipe_description", "value": "A delicious and healthy salmon recipe with fresh herbs", "type": "text", "description": "Recipe description (optional, max 500 characters)"}, {"key": "recipe_preparation_time", "value": "15", "type": "text", "description": "Preparation time in minutes (required, number)"}, {"key": "recipe_cook_time", "value": "20", "type": "text", "description": "Cooking time in minutes (required, number)"}, {"key": "recipe_serving_size", "value": "4", "type": "text", "description": "Number of servings (required, number)"}, {"key": "recipe_status", "value": "draft", "type": "text", "description": "Recipe status (enum: draft, published, archived)"}, {"key": "has_recipe_public_visibility", "value": "false", "type": "text", "description": "Public visibility flag (boolean)"}, {"key": "has_recipe_private_visibility", "value": "true", "type": "text", "description": "Private visibility flag (boolean)"}, {"key": "categories", "value": "[1, 2]", "type": "text", "description": "Category IDs (required, array of numbers)"}, {"key": "ingredients", "value": "[{\"id\": 1, \"quantity\": 500, \"measure\": 1, \"wastage\": 5.0, \"cost\": 15.99}]", "type": "text", "description": "Recipe ingredients (required, array of objects)"}, {"key": "steps", "value": "[{\"order\": 1, \"description\": \"Preheat grill to medium-high heat\"}, {\"order\": 2, \"description\": \"Season salmon with herbs\"}]", "type": "text", "description": "Recipe steps (required, array of objects)"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "Recipe files (max 10 files, formats: jpg, png, pdf, doc, max 5MB each)"}, {"key": "stepImages", "type": "file", "src": [], "description": "Step images (max 20 files, formats: jpg, png, max 5MB each)"}, {"key": "recipePlaceholder", "type": "file", "src": [], "description": "Recipe placeholder/thumbnail image"}]}, "url": {"raw": "{{baseUrl}}/recipes/create", "host": ["{{baseUrl}}"], "path": ["recipes", "create"]}}}, {"name": "Get Current Recipe State (Before Update)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Recipe data exists', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.data).to.exist;", "});", "", "// Store current recipe data for comparison", "const jsonData = pm.response.json();", "pm.collectionVariables.set('currentRecipe', JSON.stringify(jsonData.data));", "", "console.log('=== CURRENT RECIPE STATE ===');", "console.log('Title:', jsonData.data.recipe_title);", "console.log('Placeholder:', jsonData.data.recipe_placeholder || 'None');", "console.log('Resources:', jsonData.data.resources?.length || 0);", "console.log('Steps:', jsonData.data.steps?.length || 0);", "", "if (jsonData.data.resources?.length > 0) {", "    console.log('Current Resources:');", "    jsonData.data.resources.forEach((resource, index) => {", "        console.log(`  ${index + 1}. ID: ${resource.item_id}, Type: ${resource.type}, Status: ${resource.status}`);", "    });", "}", "", "if (jsonData.data.steps?.length > 0) {", "    console.log('Current Steps:');", "    jsonData.data.steps.forEach((step) => {", "        console.log(`  ${step.recipe_step_order}. ${step.recipe_step_description} (Image ID: ${step.item_id || 'None'})`);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/get-by-id/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "get-by-id", "{{recipeId}}"]}}}, {"name": "Smart Merge Update - Complete Test (3 New + 2 Existing + Links)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Update successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.message).to.exist;", "});", "", "console.log('=== SMART MERGE COMPLETE UPDATE ===');", "console.log('Status:', pm.response.json().status);", "console.log('Message:', pm.response.json().message);", "console.log('Expected: 2 existing + 3 new files + 3 links = 8 total resources');"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Smart Merge Test Recipe - Complete Update", "type": "text", "description": "Recipe title (required)"}, {"key": "recipe_description", "value": "Testing 3 new files + 2 existing files + links scenario with Smart Merge", "type": "text", "description": "Recipe description"}, {"key": "recipe_preparation_time", "value": "25", "type": "text", "description": "Preparation time in minutes"}, {"key": "recipe_cook_time", "value": "40", "type": "text", "description": "Cook time in minutes"}, {"key": "recipePlaceholder", "type": "file", "src": [], "description": "NEW: Upload new placeholder image file"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "NEW: Upload resource file 1 (PDF/DOC/Image)"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "NEW: Upload resource file 2 (PDF/DOC/Image)"}, {"key": "recipeFiles", "type": "file", "src": [], "description": "NEW: Upload resource file 3 (PDF/DOC/Image)"}, {"key": "stepImages", "type": "file", "src": [], "description": "NEW: Upload step image 1"}, {"key": "stepImages", "type": "file", "src": [], "description": "NEW: Upload step image 2"}, {"key": "resources", "value": "[\n  {\n    \"item_id\": 101,\n    \"type\": \"item\"\n  },\n  {\n    \"item_id\": 102,\n    \"type\": \"item\"\n  },\n  {\n    \"type\": \"link\",\n    \"item_link\": \"https://youtube.com/watch?v=test-recipe-demo\",\n    \"item_link_type\": \"youtube\"\n  },\n  {\n    \"type\": \"link\",\n    \"item_link\": \"https://example.com/recipe-nutrition-guide.pdf\",\n    \"item_link_type\": \"pdf\"\n  },\n  {\n    \"type\": \"link\",\n    \"item_link\": \"https://example.com/cooking-tips-blog\",\n    \"item_link_type\": \"link\"\n  }\n]", "type": "text", "description": "EXISTING: 2 existing files (item_id 101, 102) + 3 link resources. Change item_ids to match your data."}, {"key": "steps", "value": "[\n  {\n    \"order\": 1,\n    \"description\": \"Prepare ingredients and wash vegetables thoroughly\",\n    \"item_id\": 301\n  },\n  {\n    \"order\": 2,\n    \"description\": \"Heat oil in a large pan over medium heat\"\n  },\n  {\n    \"order\": 3,\n    \"description\": \"Add onions and cook until translucent\",\n    \"item_id\": 303\n  },\n  {\n    \"order\": 4,\n    \"description\": \"Add spices and cook for 2 minutes\"\n  },\n  {\n    \"order\": 5,\n    \"description\": \"Season with salt and pepper to taste\"\n  }\n]", "type": "text", "description": "MIXED: 5 steps - 2 with existing images (item_id 301, 303), 2 with new images, 1 without image. Change item_ids to match your data."}, {"key": "ingredients", "value": "[\n  {\n    \"id\": 1,\n    \"quantity\": 2,\n    \"measure\": 1,\n    \"ingredient_name\": \"Rice\"\n  },\n  {\n    \"id\": 2,\n    \"quantity\": 1,\n    \"measure\": 2,\n    \"ingredient_name\": \"Olive Oil\"\n  }\n]", "type": "text", "description": "Recipe ingredients"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}, {"name": "Smart Merge Update - Keep All Existing (No Media Changes)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Basic info update successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "});", "", "console.log('=== KEEP ALL EXISTING MEDIA TEST ===');", "console.log('Status:', pm.response.json().status);", "console.log('Expected: All existing media preserved, no uploads processed');"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Basic Info Update Only - Media Preserved", "type": "text", "description": "Only update basic info"}, {"key": "recipe_description", "value": "Testing that existing media is preserved when no media fields are provided", "type": "text", "description": "Updated description"}, {"key": "recipe_preparation_time", "value": "30", "type": "text", "description": "Updated prep time"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}, {"name": "Smart Merge Update - Remove All Resources", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Resources removal successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "});", "", "console.log('=== REMOVE ALL RESOURCES TEST ===');", "console.log('Status:', pm.response.json().status);", "console.log('Expected: All existing resources deactivated');"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Remove All Resources Test", "type": "text", "description": "Recipe title"}, {"key": "resources", "value": "[]", "type": "text", "description": "Empty array to remove all resources"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}, {"name": "Smart Merge Update - Edge Case (Invalid Item IDs)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Invalid item_ids handled gracefully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "});", "", "console.log('=== INVALID ITEM IDS TEST ===');", "console.log('Status:', pm.response.json().status);", "console.log('Expected: Invalid item_ids skipped or set to null');"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Invalid Item IDs Test", "type": "text", "description": "Recipe title"}, {"key": "resources", "value": "[\n  {\n    \"item_id\": 999999,\n    \"type\": \"item\"\n  },\n  {\n    \"item_id\": 101,\n    \"type\": \"item\"\n  }\n]", "type": "text", "description": "Mix of invalid (999999) and valid (101) item_ids"}, {"key": "steps", "value": "[\n  {\n    \"order\": 1,\n    \"description\": \"Test step with invalid item_id\",\n    \"item_id\": 999999\n  },\n  {\n    \"order\": 2,\n    \"description\": \"Test step with valid item_id\",\n    \"item_id\": 301\n  }\n]", "type": "text", "description": "Mix of invalid and valid step item_ids"}]}, "url": {"raw": "{{baseUrl}}/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "update", "{{recipeId}}"]}}}, {"name": "Verify Updated Recipe State (After Smart Merge)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Recipe updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.data).to.exist;", "});", "", "// Compare with original state", "const currentRecipe = JSON.parse(pm.collectionVariables.get('currentRecipe') || '{}');", "const updatedRecipe = pm.response.json().data;", "", "console.log('=== SMART MERGE VALIDATION RESULTS ===');", "console.log('Title Updated:', updatedRecipe.recipe_title);", "console.log('Placeholder Updated:', updatedRecipe.recipe_placeholder !== currentRecipe.recipe_placeholder);", "", "// Count active resources", "const activeResources = updatedRecipe.resources?.filter(r => r.status === 'active') || [];", "const fileResources = activeResources.filter(r => r.type === 'item');", "const linkResources = activeResources.filter(r => r.type === 'link');", "", "console.log('Active Resources:', activeResources.length);", "console.log('- File Resources:', fileResources.length);", "console.log('- Link Resources:', linkResources.length);", "", "// Check steps", "console.log('Total Steps:', updatedRecipe.steps?.length || 0);", "const stepsWithImages = updatedRecipe.steps?.filter(s => s.item_id) || [];", "console.log('Steps with Images:', stepsWithImages.length);", "", "// Validation tests", "pm.test('Smart Merge working correctly', function () {", "    pm.expect(activeResources.length).to.be.at.least(1);", "    pm.expect(updatedRecipe.steps?.length).to.be.at.least(1);", "});", "", "console.log('=== SMART MERGE VALIDATION COMPLETE ===');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/get-by-id/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "get-by-id", "{{recipeId}}"]}}}]}, {"name": "Additional Recipe Endpoints", "description": "Other recipe management endpoints", "item": [{"name": "List Recipes", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Recipe list retrieved', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.be.true;", "    pm.expect(jsonData.data).to.exist;", "    pm.expect(jsonData.data.items).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/list?page=1&limit=10&search=&status=published&visibility=public&sort_by=created_at&sort_order=DESC", "host": ["{{baseUrl}}"], "path": ["recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10, max: 100)"}, {"key": "search", "value": "", "description": "Search in title, description (min 3 characters)"}, {"key": "status", "value": "published", "description": "Filter by status (draft, published, archived)"}, {"key": "visibility", "value": "public", "description": "Filter by visibility (public, private)"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, updated_at, title)"}, {"key": "sort_order", "value": "DESC", "description": "Sort order (ASC, DESC)"}]}}}, {"name": "Archive Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/archive/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "archive", "{{recipeId}}"]}}}, {"name": "Publish Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/publish/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "publish", "{{recipeId}}"]}}}, {"name": "Make Recipe Public", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/make-public/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "make-public", "{{recipeId}}"]}}}, {"name": "Get Recipe History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/history/{{recipeId}}?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["recipes", "history", "{{recipeId}}"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}]}}}, {"name": "Duplicate Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/duplicate/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "duplicate", "{{recipeId}}"]}}}, {"name": "Export Recipe", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/export/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "export", "{{recipeId}}"]}}}, {"name": "Delete Recipe", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/recipes/delete/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["recipes", "delete", "{{recipeId}}"]}}}]}, {"name": "Public Recipe Module", "description": "Public Recipe APIs (no authentication required)", "item": [{"name": "Get Public Recipes List", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/recipes/list?page=1&limit=10&search=&sort_by=created_at&sort_order=DESC", "host": ["{{baseUrl}}"], "path": ["public", "recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "search", "value": "", "description": "Search term"}, {"key": "sort_by", "value": "created_at", "description": "Sort field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order"}]}}}, {"name": "Get Public Recipe By ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/recipes/get-by-id/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["public", "recipes", "get-by-id", "{{recipeId}}"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "authToken", "value": "your-auth-token-here", "type": "string"}, {"key": "recipeId", "value": "1", "type": "string"}]}