{"info": {"name": "Recipe Management System - Additional Endpoints", "description": "Additional endpoints for the Recipe Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "item": [{"name": "Publish Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"has_recipe_public_visibility\": true,\n    \"has_recipe_private_visibility\": true\n}"}, "url": {"raw": "{{baseUrl}}/private/recipes/publish/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "publish", "{{recipeId}}"]}, "description": "Change recipe status from draft to published with visibility settings"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "success", "message": "Recipe published successfully", "data": {"id": "{{recipeId}}", "recipe_status": "published", "has_recipe_public_visibility": true, "has_recipe_private_visibility": true, "updated_at": "2025-06-18T10:30:00.000Z"}}}, {"name": "Error Response - Invalid Status", "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "body": {"status": "error", "message": "Recipe cannot be published. Current status: archived"}}]}, {"name": "Archive Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/archive/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "archive", "{{recipeId}}"]}, "description": "Archive a recipe and remove all user assignments"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": {"status": "success", "message": "Recipe archived successfully"}}, {"name": "Error Response - Not Found", "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "body": {"status": "error", "message": "Recipe not found"}}]}, {"name": "Toggle Recipe Bookmark", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/bookmark/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "bookmark", "{{recipeId}}"]}, "description": "Add or remove a recipe bookmark for the current user"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": {"status": "success", "message": "Recipe bookmark toggled successfully", "data": {"is_bookmarked": true}}}]}, {"name": "Export Recipe", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/export/{{recipeId}}?format=excel", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "export", "{{recipeId}}"], "query": [{"key": "format", "value": "excel", "description": "Export format (excel, pdf, csv)"}]}, "description": "Export recipe details in different formats"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": {"status": "success", "message": "Recipe exported successfully", "data": {"download_url": "http://example.com/exports/recipe-123.xlsx"}}}]}, {"name": "Duplicate Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/duplicate/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "duplicate", "{{recipeId}}"]}, "description": "Create a copy of an existing recipe with all its relations"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": {"status": "success", "message": "Recipe duplicated successfully", "data": {"id": "new_recipe_id", "recipe_title": "Copy of Original Recipe", "recipe_slug": "copy-of-original-recipe"}}}]}, {"name": "Get Recipe History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/history/{{recipeId}}?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "history", "{{recipeId}}"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}]}, "description": "Get recipe change history with pagination"}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "body": {"status": "success", "data": {"items": [{"id": 1, "recipe_id": "{{recipeId}}", "changed_by": "<EMAIL>", "change_type": "UPDATE", "changes": {"recipe_title": {"old": "Old Title", "new": "New Title"}}, "created_at": "2025-06-18T10:00:00.000Z"}], "meta": {"total": 1, "page": 1, "limit": 20, "total_pages": 1}}}}]}]}